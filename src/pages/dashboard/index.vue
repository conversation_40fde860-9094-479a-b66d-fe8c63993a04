<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "数据看板"
  }
}
</route>

<script lang="ts" setup>
//
import { ref } from 'vue'

const source = ref([
  {
    label: '识别任务总量:',
    value: '1000000',
  },
  {
    label: '识别准确率:',
    value: '1000000',
  },
  {
    label: '审核通过率:',
    value: '1000000',
  },
  {
    label: '审核驳回率:',
    value: '1000000',
  },
  {
    label: '申诉数量:',
    value: '1000000',
  },
])
const fastSource = ref([
  {
    image: '/static/images/xinzhen.png',
    label: '新增项目',
  },
  {
    image: '/static/images/shensu.png',

    label: '申诉项目',
  },
])
const list = ref<string[]>(['项目清单', '审核报告', '识别结果'])

const current = ref('项目清单')

const detailsList = ref([
  {
    title: '机房名称1',
    code: '1231123123',
    address: '广东广州',
    type: '数据中心',
    status: '已审核',
  },
  {
    title: '机房名称2',
    code: '1231123123',
    address: '广东广州',
    type: '数据中心',
    status: '已审核',
  },
  {
    title: '机房名称3',
    code: '1231123123',
    address: '广东广州',
    type: '数据中心',
    status: '已审核',
  },
])
</script>

<template>
  <view class="global-bg-image-bg" min-h-screen flex flex-col items-center pt-20rpx>
    <wd-card type="rectangle" border-y="6rpx solid #0085D0" w-700rpx>
      <template #title>
        <view border-b="2rpx solid #0085D0" flex items-center justify-between py-4rpx>
          <text text-xl>数据看板</text>
          <text text-sm text-gray-400>更多 >></text>
        </view>
      </template>
      <view flex flex-wrap justify-between gap-8rpx py-6rpx>
        <view v-for="(item, index) in source" :key="index" w="48%" flex items-center justify-between>
          <view flex items-center>
            <img src="/static/images/shuju.png" mr-6rpx h-40rpx w-40rpx>
            <text text-xs>{{ item.label }}</text>
          </view>
          <text text-xs>{{ item.value }}</text>
        </view>
      </view>
    </wd-card>

    <wd-card type="rectangle" border-y="6rpx solid #0085D0" w-700rpx>
      <template #title>
        <view border-b="2rpx solid #0085D0" flex items-center justify-between py-4rpx>
          <text text-xl>快捷方式</text>
        </view>
      </template>
      <view w-260rpx flex flex-wrap justify-between>
        <view v-for="(item, index) in fastSource" :key="index" flex flex-col items-center>
          <img :src="item.image" h-120rpx w-120rpx>
          <text text-xs>{{ item.label }}</text>
        </view>
      </view>
    </wd-card>

    <wd-card type="rectangle" border-y="6rpx solid #0085D0" w-700rpx class="car-bg">
      <wd-segmented v-model:value="current" :options="list" border-b="2rpx solid #0085D0" />

      <view v-for="(item, index) in detailsList" :key="index" mt-20rpx bg-white p-10rpx>
        <view bg-sky-100 p-10rpx text-base>
          {{ item.title }}
        </view>
        <view pt-10rpx c-gray-600>
          编号：{{ item.code }}
        </view>
        <view pt-10rpx c-gray-600>
          机房地址：{{ item.address }}
        </view>
        <view pt-10rpx c-gray-600>
          机房类型：{{ item.type }}
        </view>
        <view pt-10rpx c-gray-600>
          任务状态：{{ item.status }}
        </view>
      </view>
    </wd-card>
  </view>
</template>

<style lang="scss" scoped>
:deep(.wd-card__title-content),
:deep(.wd-card) {
  padding: 0;
}

:deep(.wd-card) {
  padding: 0 12rpx;
}
:deep(.wd-card.is-rectangle) {
  border-radius: 12rpx;
}
:deep(.wd-card.is-rectangle .wd-card__content) {
  padding: 8rpx 0;
}
:deep(.wd-segmented__item.is-active) {
  background: #0085d026;
  color: #0085d0;
}
:deep(.wd-segmented) {
  background-color: #fff;
}
:deep(.car-bg.wd-card) {
  background-color: #ffffff80;
}
</style>
