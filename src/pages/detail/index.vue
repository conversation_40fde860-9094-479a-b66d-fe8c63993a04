<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "工单详情"
  }
}
</route>

<script lang="ts" setup>
defineOptions({
  name: 'Detail',
})

const value = ref<string[]>([
  '项目信息',
])

function handleEdit() {

}

function handleDelete() {

}
</script>

<template>
  <view class="">
    <wd-collapse v-model="value">
      <wd-collapse-item name="项目信息">
        <template #title="{ expanded }">
          <view flex="~ justify-between" gap-10px>
            <!-- title -->
            <view flex-1>
              <text>项目信息</text>
            </view>
            <!-- other -->
            <view item-align-flex-start flex gap-10px>
              <wd-button type="warning" :round="false" size="small" @click.stop="handleEdit">
                编辑
              </wd-button>
              <wd-button type="error" :round="false" size="small" @click.stop="handleDelete">
                删除
              </wd-button>
            </view>
            <!-- icon -->
            <view
              transition="transform 0.3s"
              :class="`${expanded ? 'rotate--180' : ''}`"
            >
              <wd-icon name="arrow-down" />
            </view>
          </view>
        </template>
        <!-- content start -->
        这是内容
        <!-- content end -->
      </wd-collapse-item>
    </wd-collapse>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
