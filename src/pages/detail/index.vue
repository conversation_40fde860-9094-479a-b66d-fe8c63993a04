<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "工单详情"
  }
}
</route>

<script lang="ts" setup>
import CollapsePanelItem from '@/components/CollapsePanelItem/CollapsePanelItem.vue'

defineOptions({
  name: 'Detail',
})

const value = ref<string[]>([
  '项目信息',
])

function handleEdit() {
  console.log('编辑操作')
}

function handleDelete() {
  console.log('删除操作')
}
</script>

<template>
  <view class="">
    <wd-collapse v-model="value">
      <!-- 使用抽象后的组件 -->
      <CollapsePanelItem title="项目信息" name="项目信息">
        <!-- header-tools 插槽：传入操作按钮 -->
        <template #header-tools>
          <view item-align-flex-start flex gap-10px>
            <wd-button type="warning" :round="false" size="small" @click.stop="handleEdit">
              编辑
            </wd-button>
            <wd-button type="error" :round="false" size="small" @click.stop="handleDelete">
              删除
            </wd-button>
          </view>
        </template>

        <!-- 默认插槽：内容区域 -->
        这是内容
      </CollapsePanelItem>
    </wd-collapse>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
