<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "工单详情"
  }
}
</route>

<script lang="ts" setup>
import type { TagType } from 'wot-design-uni/components/wd-tag/types'
import CollapsePanelItem from '@/components/CollapsePanelItem/CollapsePanelItem.vue'

defineOptions({
  name: 'Detail',
})

// 项目详情数据
interface ProjectDetail {
  name: string
  code: string
  address: string
  type: string
  stage: string
  frameName: string
  executionCity: string
  constructionUnit: string
  districtResponsible: string
  cityResponsible: string
  supervisionUnit: string
  supervisor: string
  constructionLeader: string
  completionTime: string
  taskStatus: string
  creator: string
  createTime: string
}

const projectDetail = ref<ProjectDetail>({
  name: 'XXXXXXXXXXXX',
  code: 'XXXXXXXXXXXXXXXX',
  address: 'XXXXXXXXXXXXX',
  type: '生产楼',
  stage: 'XXXX',
  frameName: 'XXXXXXXXXXXXXXXXX',
  executionCity: 'XXXXXXXXXXXXX',
  constructionUnit: 'XXXXXXXXXXXXXXXXX',
  districtResponsible: 'XXX',
  cityResponsible: 'XXX',
  supervisionUnit: 'XXXXXXXXXXXXX',
  supervisor: 'XXX',
  constructionLeader: 'XXX',
  completionTime: 'XXXXXXXXX',
  taskStatus: '图像待提交',
  creator: 'XXX',
  createTime: '2025-08-15 11:25:00',
})

const value = ref<string[]>([
  '项目信息',
])

// 获取机房类型标签样式
function getTypeTagType(type: string): TagType {
  return type === '汇聚机房' ? 'primary' : 'primary'
}

// 获取任务状态标签样式
function getStatusTagType(status: string): TagType {
  switch (status) {
    case '图像待提交':
      return 'warning'
    case '待审核':
      return 'primary'
    case '已完成':
      return 'success'
    default:
      return 'warning'
  }
}

function handleEdit() {
  console.log('编辑操作')
}

function handleDelete() {
  console.log('删除操作')
}
</script>

<template>
  <view class="global-bg-gray" min-h-screen p-y-16rpx pb-80px>
    <wd-collapse v-model="value" m-x-16rpx>
      <CollapsePanelItem title="项目信息" name="项目信息">
        <template #header-tools>
          <view item-align-flex-start flex gap-10px>
            <wd-button type="warning" :round="false" size="small" @click.stop="handleEdit">
              编辑
            </wd-button>
            <wd-button type="error" :round="false" size="small" @click.stop="handleDelete">
              删除
            </wd-button>
          </view>
        </template>
        <view class="project-detail">
          <!-- 基本信息行 -->
          <view class="detail-row">
            <text class="label">机房名称：</text>
            <text class="value">{{ projectDetail.name }}</text>
          </view>

          <view class="detail-row">
            <text class="label">编号：</text>
            <text class="value">{{ projectDetail.code }}</text>
          </view>

          <view class="detail-row">
            <text class="label">机房地址：</text>
            <text class="value">{{ projectDetail.address }}</text>
          </view>

          <view class="detail-row">
            <text class="label">机房类型：</text>
            <wd-tag :type="getTypeTagType(projectDetail.type)" size="small">
              {{ projectDetail.type }}
            </wd-tag>
            <text class="label ml-[20px]">项目阶段：</text>
            <text class="value">{{ projectDetail.stage }}</text>
          </view>

          <view class="detail-row">
            <text class="label">框架名称：</text>
            <text class="value">{{ projectDetail.frameName }}</text>
          </view>

          <view class="detail-row">
            <text class="label">执行地市：</text>
            <text class="value">{{ projectDetail.executionCity }}</text>
          </view>

          <view class="detail-row">
            <text class="label">施工单位：</text>
            <text class="value">{{ projectDetail.constructionUnit }}</text>
          </view>

          <view class="detail-row">
            <text class="label">区公司机房建设负责人：</text>
            <text class="value">{{ projectDetail.districtResponsible }}</text>
            <text class="label ml-[20px]">地市机房建设负责人：</text>
            <text class="value">{{ projectDetail.cityResponsible }}</text>
          </view>

          <view class="detail-row">
            <text class="label">监理单位：</text>
            <text class="value">{{ projectDetail.supervisionUnit }}</text>
          </view>

          <view class="detail-row">
            <text class="label">监理员：</text>
            <text class="value">{{ projectDetail.supervisor }}</text>
            <text class="label ml-[20px]">施工队长：</text>
            <text class="value">{{ projectDetail.constructionLeader }}</text>
          </view>

          <view class="detail-row">
            <text class="label">收集完成时限：</text>
            <text class="value">{{ projectDetail.completionTime }}</text>
          </view>

          <view class="detail-row">
            <text class="label">任务状态：</text>
            <wd-tag :type="getStatusTagType(projectDetail.taskStatus)" size="small">
              {{ projectDetail.taskStatus }}
            </wd-tag>
          </view>

          <view class="detail-row">
            <text class="label">创建人：</text>
            <text class="value">{{ projectDetail.creator }}</text>
          </view>

          <view class="detail-row">
            <text class="label">创建时间：</text>
            <text class="value">{{ projectDetail.createTime }}</text>
          </view>
        </view>
      </CollapsePanelItem>
    </wd-collapse>
    <view class="fixed-bottom">
      <wd-button type="primary" :round="false" block>
        图片上传
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.project-detail {
  padding: 16px;

  .detail-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    flex-wrap: wrap;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #666;
      font-size: 14px;
      margin-right: 8px;
      white-space: nowrap;
    }

    .value {
      color: #333;
      font-size: 14px;
      flex: 1;
      word-break: break-all;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}
</style>
