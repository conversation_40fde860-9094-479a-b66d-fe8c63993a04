<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "待办"
  }
}
</route>

<script lang="ts" setup>
defineOptions({
  name: 'Todo',
})

interface TodoItemList {
  name: string
  status?: string
}

interface TodoListItem {
  name: string
  color: string
  color50Opacity: string
  color5Opacity: string
  list: TodoItemList[]
}

interface TodoList extends Array<TodoListItem> {}

// 测试无数据状态时，使用空数组
const todoList = ref<TodoList>([])

// 有数据时的完整数据，可以取消注释来测试有数据的状态
/*
const todoList = ref<TodoList>([
  {
    name: '待提交照片',
    color: 'rgba(0, 133, 208, 1)',
    color50Opacity: 'rgba(0, 133, 208, 0.5)',
    color5Opacity: 'rgba(0, 133, 208, 0.05)',
    list: [
      {
        name: '机房名称111',
      },
      {
        name: '机房名称111',
      },
      {
        name: '机房名称111',
      },
    ],
  },
  {
    name: '待审核',
    color: 'rgb(214, 146, 81)',
    color50Opacity: 'rgba(214, 146, 81, 0.5)',
    color5Opacity: 'rgba(214, 146, 81, 0.05)',
    list: [
      {
        name: '机房名称111',
        status: '人工审核',
      },
      {
        name: '机房名称111',
        status: '申述审核',
      },
      {
        name: '机房名称111',
        status: '申述审核',
      },
    ],
  },
  {
    name: '待阅信息',
    color: '#3DCCCC',
    color50Opacity: '#3DCCCC80',
    color5Opacity: '#3DCCCC0D',
    list: [
      {
        name: 'xxx',
      },
      {
        name: 'yyy',
      },
      {
        name: 'zzz',
      },
    ],
  },
])
*/

function handleHeaderClick(list: TodoListItem) {
  uni.navigateTo({
    url: `/pages/list/index?type=${list.name}`,
  })
}

function handleListClick(todo: TodoListItem, list: TodoItemList) {
  uni.navigateTo({
    url: `/pages/list/index?name=${list.name}&todoName=${todo.name}`,
  })
}

function handleStatusClick(todo: TodoListItem, list: TodoItemList) {
  uni.navigateTo({
    url: `/pages/list/index?name=${list.name}&todoName=${todo.name}`,
  })
}
</script>

<template>
  <!-- TODO 布局溢出，带滚动条 -->
  <view class="global-bg-image-bg" min-h-screen flex flex-col overflow-hidden>
    <!-- 当有待办数据时显示列表 -->
    <template v-if="todoList.length > 0">
      <wd-card
        v-for="todo in todoList"
        :key="todo.name"
        custom-class="w-630rpx first:!mt-12px"
        custom-content-class="text-black"
        border-y="3px solid rd-5px"
        :style="{ borderColor: todo.color }"
      >
        <template #title>
          <view
            flex items-center justify-between
            p-b-10px
            border-b="1px solid rd-5px"
            :style="{ borderColor: todo.color }"
            @click="handleHeaderClick(todo)"
          >
            <text>
              {{ todo.name }}
            </text>
            <text text-sm text-gray>
              更多&gt;&gt;
            </text>
          </view>
        </template>
        <!-- content -->
        <view
          v-for="(list, index) in todo.list"
          :key="list.name + index"
          border-b="1px solid"
          my-10px flex items-center justify-between border-rd-5px px-3px py-5px first:mt-0
          :style="{ backgroundColor: todo.color5Opacity, borderColor: todo.color50Opacity }"
          @click="handleListClick(todo, list)"
        >
          <text>
            {{ list.name }}
          </text>
          <view v-if="list.status" @click="handleStatusClick(todo, list)">
            <wd-tag type="warning" plain>
              {{ list.status }}
            </wd-tag>
          </view>
        </view>
      </wd-card>
    </template>

    <!-- 当无待办数据时显示空状态提示 -->
    <template v-else>
      <view class="empty-container" min-h-screen flex flex-col items-center justify-center>
        <wd-status-tip
          image="content"
          tip="暂无待办事项"
          :image-size="{ width: 200, height: 150 }"
        />
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
