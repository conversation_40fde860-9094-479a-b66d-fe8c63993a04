<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "项目管理"
  }
}
</route>

<script lang="ts" setup>
defineOptions({
  name: 'List',
})

// 项目数据接口定义
interface ProjectItem {
  id: string
  name: string
  code: string
  address: string
  type: string
  status: string
}

// 模拟项目数据
const projectList = ref<ProjectItem[]>([
  {
    id: '1',
    name: '机房名称1111',
    code: '0123456789',
    address: '广东省广州市*******',
    type: '生产楼',
    status: '图像待提交',
  },
  {
    id: '2',
    name: '机房名称2222',
    code: '0123456789',
    address: '广东省广州市*******',
    type: '生产楼',
    status: '图像待提交',
  },
  {
    id: '3',
    name: '机房名称3333',
    code: '0123456789',
    address: '广东省广州市*******',
    type: '汇聚机房',
    status: '图像待提交',
  },
  {
    id: '4',
    name: '机房名称4444',
    code: '0123456789',
    address: '广东省广州市*******',
    type: '生产楼',
    status: '图像待提交',
  },
])

// 处理查询条件点击
function handleQueryClick() {
  uni.showToast({
    title: '填写查询条件',
    icon: 'none',
  })
}

// 处理项目新增点击
function handleAddProject() {
  uni.showToast({
    title: '项目新增',
    icon: 'none',
  })
}

// 处理项目项点击
function handleProjectClick(project: ProjectItem) {
  uni.showToast({
    title: `点击了${project.name}`,
    icon: 'none',
  })
}

// 获取机房类型标签样式
function getTypeTagType(type: string) {
  return type === '汇聚机房' ? 'primary' : 'default'
}

// 获取任务状态标签样式
function getStatusTagType(status: string) {
  switch (status) {
    case '图像待提交':
      return 'warning'
    case '待审核':
      return 'primary'
    case '已完成':
      return 'success'
    default:
      return 'warning'
  }
}
</script>

<template>
  <view class="global-bg-gray" min-h-screen>
    <!-- 头部区域 -->
    <view class="header-section" p-x-30rpx p-t-20rpx>
      <!-- 项目管理标题和统计 -->
      <view class="title-section" m-b-20rpx flex items-center justify-between>
        <text class="title-text" text-32rpx font-bold color="#303133">
          项目管理(共{{ projectList.length }}条)
        </text>
        <wd-button
          type="primary"
          size="small"
          :round="false"
          custom-class="query-btn"
          @click="handleQueryClick"
        >
          填写查询条件
        </wd-button>
      </view>

      <!-- 项目新增按钮 -->
      <view class="add-section" m-b-30rpx>
        <wd-button
          type="success"
          size="small"
          :round="false"
          custom-class="add-btn"
          @click="handleAddProject"
        >
          项目新增
        </wd-button>
      </view>
    </view>

    <!-- 项目列表 -->
    <view class="project-list" p-x-30rpx>
      <wd-card
        v-for="project in projectList"
        :key="project.id"
        custom-class="project-card"
        custom-content-class="project-content"
        @click="handleProjectClick(project)"
      >
        <!-- 机房名称标题 -->
        <template #title>
          <view class="project-title" bg="#E3F2FD" border-rd-8rpx p-x-20rpx p-y-15rpx>
            <text text-32rpx font-bold color="#1976D2">
              {{ project.name }}
            </text>
          </view>
        </template>

        <!-- 项目详情内容 -->
        <view class="project-details" p-20rpx>
          <!-- 编号 -->
          <view class="detail-row" m-b-20rpx flex items-center>
            <text class="label" text-28rpx color="#666">编号：</text>
            <text class="value" text-28rpx color="#303133">{{ project.code }}</text>
          </view>

          <!-- 机房地址 -->
          <view class="detail-row" m-b-20rpx flex items-center>
            <text class="label" text-28rpx color="#666">机房地址：</text>
            <text class="value" text-28rpx color="#303133">{{ project.address }}</text>
          </view>

          <!-- 机房类型 -->
          <view class="detail-row" m-b-20rpx flex items-center>
            <text class="label" text-28rpx color="#666">机房类型：</text>
            <wd-tag
              :type="getTypeTagType(project.type)"
              size="small"
              custom-class="type-tag"
            >
              {{ project.type }}
            </wd-tag>
          </view>

          <!-- 任务状态 -->
          <view class="detail-row" flex items-center>
            <text class="label" text-28rpx color="#666">任务状态：</text>
            <wd-tag
              :type="getStatusTagType(project.status)"
              size="small"
              custom-class="status-tag"
            >
              {{ project.status }}
            </wd-tag>
          </view>
        </view>
      </wd-card>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.header-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0 0 20rpx 20rpx;
  margin-bottom: 20rpx;
}

.title-text {
  flex: 1;
}

.query-btn {
  --wot-button-small-height: 60rpx;
  --wot-button-small-padding: 0 20rpx;
}

.add-btn {
  --wot-button-small-height: 60rpx;
  --wot-button-small-padding: 0 20rpx;
}

.project-card {
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.project-title {
  margin: -20rpx -20rpx 0 -20rpx;
}

.project-content {
  padding: 0;
}

.detail-row {
  .label {
    min-width: 160rpx;
    flex-shrink: 0;
  }

  .value {
    flex: 1;
  }
}

.type-tag,
.status-tag {
  --wot-tag-small-height: 48rpx;
  --wot-tag-small-padding: 0 16rpx;
  --wot-tag-small-font-size: 24rpx;
}
</style>
