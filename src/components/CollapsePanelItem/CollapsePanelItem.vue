<script lang="ts" setup>
interface Props {
  /** 面板标题 */
  title: string
  /** 面板名称，用于v-model绑定 */
  name: string
}

defineOptions({
  name: 'CollapsePanelItem',
})

defineProps<Props>()
</script>

<template>
  <wd-collapse-item :name="name">
    <template #title="{ expanded }">
      <view flex="~ justify-between" gap-10px>
        <!-- title -->
        <view flex-1>
          <text>{{ title }}</text>
        </view>

        <!-- header-tools 插槽：用于放置操作按钮等工具 -->
        <view v-if="$slots['header-tools']" item-align-flex-start flex gap-10px>
          <slot name="header-tools" :expanded="expanded" />
        </view>

        <!-- icon -->
        <view
          transition="transform 0.3s"
          :class="`${expanded ? 'rotate--180' : ''}`"
        >
          <wd-icon name="arrow-down" />
        </view>
      </view>
    </template>

    <!-- 默认插槽：内容区域 -->
    <slot />
  </wd-collapse-item>
</template>

<style lang="scss" scoped>
//
</style>
