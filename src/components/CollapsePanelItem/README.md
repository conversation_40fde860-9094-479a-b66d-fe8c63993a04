# CollapsePanelItem 组件

一个可复用的折叠面板项组件，基于 wot-design-uni 的 wd-collapse-item 封装。

## 功能特性

- ✅ 支持自定义标题
- ✅ 支持头部工具栏插槽（操作按钮等）
- ✅ 支持默认内容插槽
- ✅ 自动处理展开/收起动画
- ✅ TypeScript 类型支持

## Props

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| title | string | ✅ | - | 面板标题 |
| name | string | ✅ | - | 面板名称，用于v-model绑定 |
| expanded | boolean | ❌ | false | 是否展开（由父组件的v-model控制） |

## 插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 内容区域 | - |
| header-tools | 头部工具栏区域 | { expanded: boolean } |

## 使用示例

### 基础用法

```vue
<template>
  <wd-collapse v-model="activeNames">
    <CollapsePanelItem title="基础信息" name="basic">
      这是基础信息的内容
    </CollapsePanelItem>
  </wd-collapse>
</template>

<script setup>
import CollapsePanelItem from '@/components/CollapsePanelItem/CollapsePanelItem.vue'

const activeNames = ref(['basic'])
</script>
```

### 带操作按钮

```vue
<template>
  <wd-collapse v-model="activeNames">
    <CollapsePanelItem title="项目信息" name="project">
      <template #header-tools>
        <wd-button type="warning" size="small" @click.stop="handleEdit">
          编辑
        </wd-button>
        <wd-button type="error" size="small" @click.stop="handleDelete">
          删除
        </wd-button>
      </template>
      
      <!-- 项目详细信息 -->
      <view class="project-info">
        <text>项目名称：示例项目</text>
        <text>创建时间：2024-01-01</text>
      </view>
    </CollapsePanelItem>
  </wd-collapse>
</template>
```

### 多个面板

```vue
<template>
  <wd-collapse v-model="activeNames">
    <CollapsePanelItem title="基础信息" name="basic">
      基础信息内容
    </CollapsePanelItem>
    
    <CollapsePanelItem title="详细配置" name="config">
      <template #header-tools>
        <wd-button type="primary" size="small" @click.stop="handleConfig">
          配置
        </wd-button>
      </template>
      详细配置内容
    </CollapsePanelItem>
    
    <CollapsePanelItem title="操作日志" name="logs">
      操作日志内容
    </CollapsePanelItem>
  </wd-collapse>
</template>
```

## 注意事项

1. **必须在 wd-collapse 内使用**：该组件是对 wd-collapse-item 的封装
2. **事件阻止冒泡**：在 header-tools 插槽中的按钮需要使用 `@click.stop` 防止触发面板展开/收起
3. **name 属性唯一性**：确保每个面板的 name 属性在同一个 collapse 中是唯一的

## 样式定制

组件继承了 wot-design-uni 的样式系统，可以通过 CSS 变量进行定制：

```scss
:deep(.wd-collapse-item) {
  // 自定义样式
}
```
